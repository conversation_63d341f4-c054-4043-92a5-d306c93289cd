import { useState } from "react"
import Navbar from "../../components/navbar/Navbar"
import FilterBar from "../../components/filter-bar/FilterBar"
import InteractiveMap, {
	type Territory,
} from "../../components/map/InteractiveMap" // fix path
import SelectedTerritories from "../../components/territories/SelectedTerritories"
import Results from "../../components/results/Results"

export function Desktop() {
	const [active, setActive] = useState<
		"territory-builder" | "assumptions" | "results"
	>("territory-builder")
	const [selectedTerritories, setSelectedTerritories] = useState<Territory[]>(
		[]
	)

	const handleRemoveTerritory = (id: string) => {
		setSelectedTerritories((prev) => prev.filter((t) => t.id !== id))
	}

	// TODO: Replace this with actual logic
	const projectCount = selectedTerritories.length * 100

	return (
		<>
			<div className="bg-white h-[380px] min-[1150px]:h-[305px] shadow-[inset_0_-10px_50px_#0000001a]">
				<Navbar activeId={active} onChange={setActive} />
				<div className="relative -mt-[185px] md:-mt-[175px]">
					<FilterBar />
				</div>
			</div>

			<section className="relative mx-auto w-[min(2360px,calc(100%-96px))] mt-12 pb-12">
				{active === "territory-builder" && (
					<div className="grid grid-cols-1 lg:grid-cols-[2fr_1fr] gap-6 lg:gap-10">
						<div className="h-[620px] min-[1150px]:h-[clamp(620px,calc(100vh-401px),calc(100vh-401px))]">
							<InteractiveMap
								selectedTerritories={selectedTerritories}
								onSelectionChange={setSelectedTerritories}
							/>
						</div>

						<div className="h-[620px] min-[1150px]:h-[clamp(620px,calc(100vh-401px),calc(100vh-401px))]">
							<SelectedTerritories
								territories={selectedTerritories}
								onRemove={handleRemoveTerritory}
								projectCount={projectCount}
							/>
						</div>
					</div>
				)}

				{active === "results" && (
					<Results
						territories={selectedTerritories}
						projectCount={projectCount}
					/>
				)}
			</section>
		</>
	)
}
