import { useState } from 'react'
import Navbar from '../../components/navbar/Navbar'
import FilterBar from '../../components/filter-bar/FilterBar'

export function Desktop() {
  const [active, setActive] = useState<'territory-builder'|'assumptions'|'results'>('territory-builder')

  return (
    <div className="bg-white h-[380px] min-[1150px]:h-[305px] bg-white shadow-[inset_0_-10px_50px_#0000001a]">
      <Navbar activeId={active} onChange={setActive} />
      <div className="relative -mt-[185px] md:-mt-[175px]">
        <FilterBar />
      </div>
    </div>
  )
}
