import { useMemo, useRef, useState, useEffect } from 'react'
import type { Feature, FeatureCollection, Geometry } from 'geojson'
import type {
  LatLngExpression,
  PathOptions,
  Layer,
  Path,
  Map as LeafletMap,
  LatLngBounds,
} from 'leaflet'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, GeoJSON, useMap } from 'react-leaflet'
import { latLngBounds, Polygon, Polyline, FeatureGroup } from 'leaflet'
import { Plus } from 'lucide-react'

import p0 from './data/postcodesGeo-part-0.json'
import p1 from './data/postcodesGeo-part-1.json'
import p2 from './data/postcodesGeo-part-2.json'
import p3 from './data/postcodesGeo-part-3.json'
import p4 from './data/postcodesGeo-part-4.json'
import p5 from './data/postcodesGeo-part-5.json'
import p6 from './data/postcodesGeo-part-6.json'
import p7 from './data/postcodesGeo-part-7.json'

import salRows from '../../components/map/data/sal-to-poa-2021.min.json'

type poa_props = {
  POA_CODE16?: string
  POA_NAME16?: string
  POA_CODE20?: string
} & Record<string, unknown>

type sal_row = { sal_name?: string; state?: string; poa_code?: string }

export type Territory = { id: string; name: string; postcode: string }

interface InteractiveMapProps {
  selectedTerritories?: Territory[]
  onSelectionChange?: (selectedTerritories: Territory[]) => void
}

const CENTER: LatLngExpression = [-33.8688, 151.2093]

function getId(f?: Feature<Geometry, poa_props>): string {
  if (!f) return ''
  const p = f.properties || {}
  return String(p.POA_CODE16 ?? p.POA_CODE20 ?? p.POA_NAME16 ?? f.id ?? '')
}

function getName(f?: Feature<Geometry, poa_props>): string {
  if (!f) return ''
  const p = f.properties || {}
  return String(p.POA_NAME16 ?? '').split(',')[0].trim()
}

function MapGrab({ onReady }: { onReady: (m: LeafletMap) => void }) {
  const map = useMap()
  useEffect(() => {
    onReady(map)
    if (map.zoomControl) map.removeControl(map.zoomControl)
  }, [map, onReady])
  return null
}

export default function InteractiveMap({
  selectedTerritories = [],
  onSelectionChange = () => {},
}: InteractiveMapProps) {
  const [selected, setSelected] = useState<Set<string>>(
    new Set(selectedTerritories.map(t => t.id))
  )
  const [query, setQuery] = useState('')

  const mapRef = useRef<LeafletMap | null>(null)
  const boundsById = useRef<Map<string, LatLngBounds>>(new Map())
  const featuresById = useRef<Map<string, Feature<Geometry, poa_props>>>(new Map())
  const codeToId = useRef<Map<string, string>>(new Map())

  const data: FeatureCollection<Geometry, poa_props> = useMemo(() => {
    const parts: Feature<Geometry, poa_props>[] = [
      ...(p0 as Feature<Geometry, poa_props>[]),
      ...(p1 as Feature<Geometry, poa_props>[]),
      ...(p2 as Feature<Geometry, poa_props>[]),
      ...(p3 as Feature<Geometry, poa_props>[]),
      ...(p4 as Feature<Geometry, poa_props>[]),
      ...(p5 as Feature<Geometry, poa_props>[]),
      ...(p6 as Feature<Geometry, poa_props>[]),
      ...(p7 as Feature<Geometry, poa_props>[]),
    ]
    parts.forEach(f => {
      const id = getId(f)
      if (id) featuresById.current.set(id, f)
    })
    return { type: 'FeatureCollection', features: parts }
  }, [])

  const salIndex = useMemo(() => {
    const m = new Map<string, string[]>()
    for (const r of salRows as sal_row[]) {
      const name = (r.sal_name || '').toLowerCase().trim()
      const code = String(r.poa_code || '').padStart(4, '0')
      if (!name || !code) continue
      const arr = m.get(name) ?? []
      if (!arr.includes(code)) arr.push(code)
      m.set(name, arr)
    }
    return m
  }, [])

  useEffect(() => {
    const territories: Territory[] = []
    selected.forEach(id => {
      const f = featuresById.current.get(id)
      if (f) territories.push({ id, name: getName(f), postcode: id })
    })
    onSelectionChange(territories)
  }, [selected, onSelectionChange])

  useEffect(() => {
    const next = new Set(selectedTerritories.map(t => t.id))
    const changed =
      next.size !== selected.size ||
      !Array.from(next).every(id => selected.has(id))
    if (changed) setSelected(next)
  }, [selectedTerritories])

  const styleFn = (feature?: Feature<Geometry, poa_props>): PathOptions => {
    const id = getId(feature)
    const isSel = id !== '' && selected.has(id)
    return {
      color: isSel ? '#007102' : '#444444',
      weight: isSel ? 3 : 1,
      fillColor: isSel ? '#007102' : '#9ca3af',
      fillOpacity: isSel ? 0.35 : 0.12,
    }
  }

  const onEach = (feature: Feature<Geometry, poa_props>, layer: Layer) => {
    const id = getId(feature)
    feature.id = id

    if (layer instanceof Polygon || layer instanceof Polyline || layer instanceof FeatureGroup) {
      const b = layer.getBounds()
      if (id) boundsById.current.set(id, b)
    }

    const p = (feature.properties || {}) as poa_props
    const code = String(p.POA_CODE16 ?? p.POA_CODE20 ?? '').padStart(4, '0')
    if (id && code) codeToId.current.set(code, id)

    layer.on('click', () => {
      if (!id) return
      setSelected(prev => {
        const next = new Set(prev)
        next.has(id) ? next.delete(id) : next.add(id)
        return next
      })
    })

    layer.on('mouseover', () => (layer as Path).setStyle({ weight: 2 }))
    layer.on('mouseout', () => (layer as Path).setStyle({ weight: 1 }))
  }

  const remountKey = useMemo(() => Array.from(selected).sort().join(','), [selected])

  function findIds(q: string): string[] {
    const needle = q.trim().toLowerCase()
    if (!needle) return []
    if (/^\d{3,4}$/.test(needle)) {
      const id = codeToId.current.get(needle.padStart(4, '0'))
      return id ? [id] : []
    }
    const exact = salIndex.get(needle)
    if (exact?.length) {
      return exact.map(c => codeToId.current.get(c)).filter(Boolean) as string[]
    }
    const hits: string[] = []
    for (const [name, codes] of salIndex.entries()) {
      if (name.includes(needle)) {
        for (const c of codes) {
          const id = codeToId.current.get(c)
          if (id) hits.push(id)
        }
        if (hits.length >= 8) break
      }
    }
    return [...new Set(hits)]
  }

  function handleSearch(e: React.FormEvent) {
    e.preventDefault()
    const ids = findIds(query)
    if (!ids.length) return
    const m = mapRef.current
    const b0 = boundsById.current.get(ids[0])
    if (!m || !b0) return
    const ext = latLngBounds(b0.getSouthWest(), b0.getNorthEast())
    for (let i = 1; i < ids.length; i++) {
      const b = boundsById.current.get(ids[i])
      if (b) {
        ext.extend(b.getSouthWest())
        ext.extend(b.getNorthEast())
      }
    }
    m.fitBounds(ext, { padding: [24, 24] })
  }

  return (
    <div className="relative w-full
      h-[clamp(620px,calc(100vh-476px),calc(100vh-476px))]
      min-[1150px]:h-[clamp(620px,calc(100vh-401px),calc(100vh-401px))]
      bg-white rounded-[20px] shadow-[0_0_150px_#0000003f] overflow-hidden">
      <MapContainer
        center={CENTER}
        zoom={10}
        scrollWheelZoom
        className="w-full h-full"
      >
        <MapGrab onReady={(m) => (mapRef.current = m)} />

        <TileLayer
          attribution="&copy; OpenStreetMap contributors"
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        <GeoJSON
          key={remountKey}
          data={data}
          style={styleFn}
          onEachFeature={onEach}
          attribution="&copy; ABS POA 2016"
        />
      </MapContainer>

      <form
        onSubmit={handleSearch}
        className="pointer-events-auto absolute left-5 bottom-5 z-[1000] flex items-center bg-white rounded-[28px] shadow-[0_10px_40px_#0003] pl-6 pr-2 h-[52px] w-[min(420px,calc(100%-96px))]"
        aria-label="search postcode or suburb"
      >
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="flex-1 bg-transparent outline-none font-light text-[16px] text-[#222] placeholder-[#535353]"
          placeholder="Enter a postcode or suburb"
        />
        <button
          type="submit"
          className="ml-2 w-[42px] h-[42px] grid place-items-center rounded-full bg-[#f2f2f2] hover:bg-[#e9e9e9] transition-colors"
          aria-label="search"
        >
          <Plus className="w-5 h-5 text-[#222]" />
        </button>
      </form>
    </div>
  )
}
