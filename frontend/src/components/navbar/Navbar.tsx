// src/components/navbar/Navbar.tsx
import { Hammer, Brain, BarChart3, type LucideIcon } from 'lucide-react'
import logo from '../../assets/renopilot.png'

type nav_id = 'territory-builder' | 'assumptions' | 'results'
type nav_item = { id: nav_id; label: string; Icon: LucideIcon }
type props = { activeId?: nav_id; onChange?: (id: nav_id) => void }

const NAV: nav_item[] = [
  { id: 'territory-builder', label: 'Territory Builder', Icon: Hammer },
  { id: 'assumptions',       label: 'Assumptions',       Icon: Brain },
  { id: 'results',           label: 'Results',           Icon: BarChart3 },
]

export default function Navbar({ activeId = 'territory-builder', onChange }: props) {
  return (
    <header className="relative h-full bg-transparent shadow-none">
      <img
        src={logo}
        alt="renopilot"
        className="absolute left-[54px] top-[30px] w-[208px] h-[68px] select-none"
        draggable={false}
      />

      <nav className="absolute top-[140px] min-[1150px]:top-[46px] left-0 right-0 lg:left-1/2 translate-x-0 lg:-translate-x-1/2 flex items-center justify-center gap-8 min-[1150px]:gap-12">
        {NAV.map(({ id, label, Icon }) => {
          const active = id === activeId
          const isHammer = id === 'territory-builder'
          return (
            <button
              key={id}
              onClick={() => onChange?.(id)}
              className="group relative flex items-center gap-2 shrink-0 outline-none transition-transform duration-150 ease-out hover:-translate-y-0.5 focus-visible:ring-2 focus-visible:ring-neutral-300 focus-visible:ring-offset-2 focus-visible:ring-offset-white rounded"
              aria-current={active ? 'page' : undefined}
            >
              <Icon
                className={`w-[17px] md:w-[24px] h-[17px] md:h-[24px] transition-colors duration-150 ${active ? 'text-[#222222]' : 'text-[#6a6a6a] group-hover:text-[#222222]'} ${isHammer ? 'rotate-315' : ''}`}
              />
              <span
                className={`text-[16px] md:text-[22px] transition-colors duration-150 ${active ? 'font-medium text-[#222222]' : 'font-light text-[#6a6a6a] group-hover:text-[#222222]'}`}
              >
                {label}
              </span>

              {active && (
                <span className="absolute left-0 top-[28px] md:top-[38px] h-0.5 w-full bg-[#222222] origin-center scale-x-100 transition-transform duration-200 ease-out" />
              )}
            </button>
          )
        })}
      </nav>

      <button
        className="absolute right-[54px] top-[32px] w-[51px] h-[51px] grid place-items-center rounded-full bg-[#eeeeee] hover:bg-[#e6e6e6] text-[34px] font-light text-[#444444] hover:text-[#222222] transition-colors duration-150"
        aria-label="help"
      >
        ?
      </button>
    </header>
  )
}
