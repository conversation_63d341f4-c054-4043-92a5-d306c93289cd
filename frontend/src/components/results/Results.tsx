import {
	BarChart3,
	<PERSON><PERSON>dingUp,
	Users,
	Building,
	DollarSign,
	ChevronLeft,
	ChevronRight,
	Download,
} from "lucide-react"

interface ResultsProps {
	territories: any[]
	projectCount?: number
}

function PlaceholderChart({
	title,
	type = "line",
}: {
	title: string
	type?: "line" | "bar"
}) {
	return (
		<div className="bg-white border border-gray-200 rounded-xl p-4 h-70">
			<h3 className="text-sm font-medium text-gray-700 mb-3">{title}</h3>
			<div className="w-full h-53 bg-gray-50 rounded-lg flex items-center justify-center">
				{type === "line" ? (
					<TrendingUp className="w-8 h-8 text-gray-400" />
				) : (
					<BarChart3 className="w-8 h-8 text-gray-400" />
				)}
			</div>
		</div>
	)
}

function ProjectsTable() {
	const projectsData = [
		{ postcode: "2000", suburb: "Sydney", projects: 120 },
		{ postcode: "2017", suburb: "Zetland", projects: 140 },
		{ postcode: "2020", suburb: "Mascot", projects: 95 },
		{ postcode: "2022", suburb: "Bondi Junction", projects: 102 },
	]

	return (
		<div className="bg-white border border-gray-200 rounded-xl p-4 h-70">
			<h3 className="text-sm font-medium text-gray-700 mb-3">
				Potential projects by postcode
			</h3>
			<div className="overflow-hidden">
				<table className="w-full text-sm">
					<thead>
						<tr className="border-b border-gray-200">
							<th className="text-left py-2 text-gray-600 font-medium">
								Postcode
							</th>
							<th className="text-left py-2 text-gray-600 font-medium">
								Suburb
							</th>
							<th className="text-right py-2 text-gray-600 font-medium">
								Potential projects
							</th>
						</tr>
					</thead>
					<tbody>
						{projectsData.map((row, index) => (
							<tr
								key={index}
								className="border-b border-gray-100 last:border-b-0"
							>
								<td className="py-1 text-gray-900">{row.postcode}</td>
								<td className="py-1 text-gray-900">{row.suburb}</td>
								<td className="py-1 text-gray-900 text-right">
									{row.projects}
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	)
}

// Metric card component
function MetricCard({
	icon: Icon,
	title,
	value,
	subtitle,
}: {
	icon: any
	title: string
	value: string
	subtitle?: string
}) {
	return (
		<div className="bg-white border border-gray-200 rounded-xl p-4">
			<div className="flex items-center gap-3 mb-2">
				<div className="p-2 bg-red-50 rounded-lg">
					<Icon className="w-5 h-5 text-[#BF2724]" />
				</div>
				<h3 className="text-sm font-medium text-gray-700">{title}</h3>
			</div>
			<div className="text-2xl font-bold text-gray-900 mb-1">{value}</div>
			{subtitle && <div className="text-sm text-gray-500">{subtitle}</div>}
		</div>
	)
}

export default function Results({ territories }: ResultsProps) {
	return (
		<div className="h-full bg-neutral-50 rounded-[20px] shadow-[0_0_150px_#0000003f] p-8 flex flex-col backdrop-blur-sm overflow-y-auto">
			<h2 className="text-2xl mb-8 font-semibold text-gray-900">Results</h2>

			{territories.length === 0 ? (
				<div className="flex flex-col items-center justify-center h-full text-center py-12">
					<div className="p-6 bg-gray-200/50 rounded-full mb-4">
						<BarChart3 className="w-12 h-12 text-gray-400" />
					</div>
					<p className="text-gray-500 text-lg font-medium mb-2">
						No results to display
					</p>
					<p className="text-gray-400 text-sm max-w-48">
						Select territories and configure assumptions to see results
					</p>
				</div>
			) : (
				<div className="space-y-6">
					<div className="grid grid-cols-2 lg:grid-cols-[1fr_1fr] gap-6">
						<PlaceholderChart title="Product life over time" type="line" />
						<ProjectsTable />
					</div>

					<div className="grid grid-cols-1 gap-6">
						<PlaceholderChart title="Homes over time" type="line" />
						<PlaceholderChart title="Uptake over time" type="bar" />
					</div>

					<div className="mt-14 flex items-center justify-between">
						<button
							type="button"
							className="h-[60px] w-72 pl-2 pr-8 rounded-full bg-white text-[#222] hover:bg-[#fafafa] inline-flex items-center gap-2 shadow-[0_0_150px_#0000003f]"
						>
							<span className="grid place-items-center w-10 h-10 rounded-full bg-[#f2f2f2]">
								<ChevronLeft className="w-5 h-5 text-[#000]" />
							</span>
							<span className="flex-1 text-right font-light text-[16px]">
								Return to <span className="font-semibold">Assumptions</span>
							</span>
						</button>

						<div className="h-[60px] w-52 pl-7 pr-2 rounded-full bg-white text-[#222] hover:bg-[#fafafa] inline-flex items-center gap-4 shadow-[0_0_150px_#0000003f]">
							<span className="font-light text-[16px]">Export results</span>
							<button
								type="button"
								className="ml-auto w-[42px] h-[42px] grid place-items-center rounded-full bg-[#BF2724] hover:bg-[#aa0000] transition-colors"
								aria-label="Continue"
							>
								<Download className="w-5 h-5 text-white" />
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	)
}
