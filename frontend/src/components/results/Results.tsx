import {
	BarChart3,
	TrendingUp,
	Users,
	Building,
	DollarSign,
	ChevronLeft,
	ChevronRight,
	Download,
} from "lucide-react"

interface ResultsProps {
	territories: any[]
	projectCount?: number
}

function PlaceholderChart({
	title,
	type = "line",
}: {
	title: string
	type?: "line" | "bar"
}) {
	return (
		<div className="bg-white border border-gray-200 rounded-lg sm:rounded-xl p-3 sm:p-4 h-48 sm:h-56 lg:h-70">
			<h3 className="text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3">
				{title}
			</h3>
			<div className="w-full h-32 sm:h-40 lg:h-53 bg-gray-50 rounded-md sm:rounded-lg flex items-center justify-center">
				{type === "line" ? (
					<TrendingUp className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400" />
				) : (
					<BarChart3 className="w-6 h-6 sm:w-8 sm:h-8 text-gray-400" />
				)}
			</div>
		</div>
	)
}

function ProjectsTable() {
	const projectsData = [
		{ postcode: "2000", suburb: "Sydney", projects: 120 },
		{ postcode: "2017", suburb: "Zetland", projects: 140 },
		{ postcode: "2020", suburb: "Mascot", projects: 95 },
		{ postcode: "2022", suburb: "Bondi Junction", projects: 102 },
	]

	return (
		<div className="bg-white border border-gray-200 rounded-lg sm:rounded-xl p-3 sm:p-4 h-48 sm:h-56 lg:h-70">
			<h3 className="text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3">
				Potential projects by postcode
			</h3>
			<div className="overflow-x-auto overflow-y-hidden">
				<table className="w-full text-xs sm:text-sm min-w-[280px]">
					<thead>
						<tr className="border-b-2 border-gray-200">
							<th className="text-left py-1 sm:py-2 text-gray-600 font-medium">
								Postcode
							</th>
							<th className="text-left py-1 sm:py-2 text-gray-600 font-medium">
								Suburb
							</th>
							<th className="text-left py-1 sm:py-2 text-gray-600 font-medium">
								Projects
							</th>
						</tr>
					</thead>
					<tbody>
						{projectsData.map((row, index) => (
							<tr
								key={index}
								className="border-b border-transparent last:border-b-0"
							>
								<td className="py-1 text-gray-800 font-mono">{row.postcode}</td>
								<td
									className="py-1 text-gray-800 truncate max-w-[80px] sm:max-w-none"
									title={row.suburb}
								>
									{row.suburb}
								</td>
								<td className="py-1 text-gray-800 text-left font-semibold">
									{row.projects}
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	)
}

export default function Results({ territories }: ResultsProps) {
	return (
		<div className="h-full bg-neutral-50 rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] shadow-[0_0_150px_#0000003f] p-4 sm:p-6 lg:p-8 flex flex-col backdrop-blur-sm overflow-y-auto">
			<h2 className="text-xl sm:text-2xl mb-6 sm:mb-8 font-semibold text-gray-900">
				Results
			</h2>

			{territories.length === 0 ? (
				<div className="flex flex-col items-center justify-center h-full text-center py-8 sm:py-12">
					<div className="p-4 sm:p-6 bg-gray-200/50 rounded-full mb-4">
						<BarChart3 className="w-8 h-8 sm:w-12 sm:h-12 text-gray-400" />
					</div>
					<p className="text-gray-500 text-base sm:text-lg font-medium mb-2">
						No results to display
					</p>
					<p className="text-gray-400 text-sm max-w-64 sm:max-w-80 px-4">
						Select territories and configure assumptions to see results
					</p>
				</div>
			) : (
				<div className="space-y-4 sm:space-y-6">
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
						<PlaceholderChart title="Product life over time" type="line" />
						<ProjectsTable />
					</div>

					<div className="grid grid-cols-1 gap-4 sm:gap-6">
						<PlaceholderChart title="Homes over time" type="line" />
						<PlaceholderChart title="Uptake over time" type="bar" />
					</div>

					{/* Action Buttons - Mobile: Stack vertically, Desktop: Side by side */}
					<div className="mt-8 sm:mt-12 lg:mt-14 flex flex-col sm:flex-row items-center gap-4 sm:justify-between">
						<button
							type="button"
							className="w-full sm:w-auto h-12 sm:h-[60px] sm:max-w-72 px-4 sm:pl-2 sm:pr-8 rounded-full bg-white text-[#222] hover:bg-[#fafafa] inline-flex items-center gap-2 shadow-[0_0_150px_#0000003f] order-2 sm:order-1"
						>
							<span className="grid place-items-center w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-[#f2f2f2]">
								<ChevronLeft className="w-4 h-4 sm:w-5 sm:h-5 text-[#000]" />
							</span>
							<span className="flex-1 text-center sm:text-right font-light text-sm sm:text-[16px]">
								Return to <span className="font-semibold">Assumptions</span>
							</span>
						</button>

						<div className="w-full sm:w-auto h-12 sm:h-[60px] sm:max-w-52 px-4 sm:pl-7 sm:pr-2 rounded-full bg-white text-[#222] hover:bg-[#fafafa] inline-flex items-center gap-2 sm:gap-4 shadow-[0_0_150px_#0000003f] order-1 sm:order-2">
							<span className="flex-1 text-center sm:text-left font-light text-sm sm:text-[16px]">
								Export results
							</span>
							<button
								type="button"
								className="w-8 h-8 sm:w-[42px] sm:h-[42px] grid place-items-center rounded-full bg-[#BF2724] hover:bg-[#aa0000] transition-colors flex-shrink-0"
								aria-label="Export results"
							>
								<Download className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	)
}
