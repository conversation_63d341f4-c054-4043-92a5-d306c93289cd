// src/components/filter-bar/FilterBar.tsx
import { useEffect, useRef, useState } from 'react'
import { ChevronRight, ChevronDown } from 'lucide-react'

type field_props = {
  id: string
  label: string
  placeholder: string
  options?: string[]
  value?: string | null
  onChange?: (v: string) => void
}

function Field({ id, label, placeholder, options = ['option 1','option 2','option 3'], value, onChange }: field_props) {
  const [open, set_open] = useState(false)
  const root = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    const on_doc = (e: MouseEvent) => {
      if (root.current && !root.current.contains(e.target as Node)) set_open(false)
    }
    document.addEventListener('mousedown', on_doc)
    return () => document.removeEventListener('mousedown', on_doc)
  }, [])

  return (
    <div ref={root} className="relative flex flex-col gap-1 md:gap-2 min-w-[120px] md:min-w-[150px] pl-4 pr-2 md:pl-6 md:pr-4">
      <label htmlFor={id} className="font-semibold text-[#222222] text-sm md:text-base leading-none">
        {label}
      </label>

      <button
        id={id}
        type="button"
        className="inline-flex items-center font-light gap-1 md:gap-2 text-sm md:text-[12px] lg:text-[16px] md:text-base text-[#6a6a6a] hover:text-[#222222] focus:outline-none leading-none"
        aria-haspopup="listbox"
        aria-expanded={open}
        aria-controls={`${id}-listbox`}
        onClick={() => set_open(v => !v)}
      >
        {value ? (
          // show chosen option
          <span className="font-light gap-1 md:gap-2 text-sm md:text-[12px] lg:text-[16px] md:text-base text-[#6a6a6a]">{value}</span>
        ) : (
          <>
            <span>{placeholder}</span>
            <ChevronDown className={`w-4 h-4 md:w-6 md:h-6 relative top-[1px] md:top-[2px] transition-transform ${open ? 'rotate-180' : ''}`} />
          </>
        )}
      </button>
      
      {/* drop-down options */}
      {open && (
        <ul
          id={`${id}-listbox`}
          role="listbox"
          className="absolute left-4 md:left-6 right-2 md:right-4 top-full mt-2 z-20 rounded-2xl border border-[#ebebeb] bg-white shadow-[0_10px_40px_#00000026] p-2"
        >
          {options.map((opt) => (
            <li
              key={opt}
              role="option"
              tabIndex={0}
              className="px-3 py-2 rounded-xl text-sm md:text-base text-[#222] hover:bg-[#f5f5f5] focus:bg-[#f0f0f0] outline-none cursor-pointer"
              onClick={() => { onChange?.(opt); set_open(false) }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  onChange?.(opt); set_open(false)
                }
              }}
            >
              {opt}
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}

export default function FilterBar() {
  const [projectType, set_projectType] = useState<string | null>(null)
  const [dwellings, set_dwellings] = useState<string | null>(null)
  const [forecast, set_forecast] = useState<string | null>(null)

  return (
    <div className="relative z-10 mx-auto w-[min(887px,calc(100%-32px))] w-[min(887px,calc(100%-128px))] bg-white rounded-[40px] shadow-[0_0_150px_#0000003f] h-auto md:h-[110px]">

      {/* for smaller screens (mobile/tab) */}
      <div className="md:hidden px-6 py-4 space-y-4">
        <div className="grid grid-cols-[1fr_auto_1fr] items-center px-4">
          <Field id="project-type" label="Project Type" placeholder="Select an option" value={projectType} onChange={set_projectType} />
          <div className="w-px h-10 bg-[#ebebeb] mx-4" />
          <Field id="dwellings" label="Dwellings" placeholder="Select an option" value={dwellings} onChange={set_dwellings} />
        </div>

        <div className="grid grid-cols-[1fr_auto_1fr] items-center px-4">
          <Field id="forecast-period" label="Forecast Period" placeholder="Select an option" value={forecast} onChange={set_forecast} />
          <div className="w-px h-10 bg-[#ebebeb] mx-4" />
          <div className="grid place-items-right px-4">
            <button className="w-10 h-10 grid place-items-center rounded-full bg-[#BF2724] hover:bg-[#aa0000] transition-colors">
              <ChevronRight className="w-5 h-5 text-white" />
            </button>
          </div>
        </div>
      </div>

      {/* for larger screens (laptop/pc) */}
      <div className="hidden md:flex h-[110px] px-8 items-center justify-between">
        <Field id="project-type" label="Project Type" placeholder="Select an option" value={projectType} onChange={set_projectType} />
        <div className="w-px h-12 bg-[#ebebeb]" />
        <Field id="dwellings" label="Dwellings" placeholder="Select an option" value={dwellings} onChange={set_dwellings} />
        <div className="w-px h-12 bg-[#ebebeb]" />
        <Field id="forecast-period" label="Forecast Period" placeholder="Select an option" value={forecast} onChange={set_forecast} />
        <button className="ml-4 w-[52px] h-[52px] grid place-items-center rounded-full bg-[#BF2724] hover:bg-[#aa0000] transition-colors">
          <ChevronRight className="w-5 h-5 text-white" />
        </button>
      </div>
    </div>
  )
}