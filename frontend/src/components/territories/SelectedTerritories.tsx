import { <PERSON>, <PERSON>P<PERSON>, Building2, ChevronRight } from "lucide-react"
import { useState } from "react"

type Territory = {
	id: string
	name: string
	postcode: string
}

interface SelectedTerritoriesProps {
	territories: Territory[]
	onRemove: (id: string) => void
	setActive: (id: "territory-builder" | "assumptions" | "results") => void
	projectCount?: number
}

export default function SelectedTerritories({
	territories,
	onRemove,
	setActive,
	projectCount = 0,
}: SelectedTerritoriesProps) {
	const [removingId, setRemovingId] = useState<string | null>(null)
	const [isLoading, setIsLoading] = useState(false)

	const handleRemove = async (id: string) => {
		setRemovingId(id)
		// Add a small delay for smooth animation
		setTimeout(() => {
			onRemove(id)
			setRemovingId(null)
		}, 200)
	}

	const handleContinue = () => {
		setIsLoading(true)
		setActive("assumptions")
		setIsLoading(false)
	}

	return (
		<div className="h-full bg-neutral-50 rounded-[12px] sm:rounded-[16px] lg:rounded-[20px] shadow-[0_0_150px_#0000003f] p-4 sm:p-6 lg:p-8 flex flex-col backdrop-blur-sm">
			<h2 className="text-xl sm:text-2xl mb-6 sm:mb-8 font-semibold text-gray-900">
				Selected Territories
			</h2>

			<div className="flex-1 overflow-y-auto mb-6 sm:mb-8">
				{territories.length === 0 ? (
					<div className="flex flex-col items-center justify-center h-full text-center py-8 sm:py-12">
						<div className="p-4 sm:p-6 bg-gray-200/50 rounded-full mb-4">
							<MapPin className="w-8 h-8 sm:w-12 sm:h-12 text-gray-400" />
						</div>
						<p className="text-gray-500 text-base sm:text-lg font-medium mb-2">
							No territories selected
						</p>
						<p className="text-gray-400 text-sm max-w-64 sm:max-w-80 px-4">
							Click on territories on the map to add them to your selection
						</p>
					</div>
				) : (
					<div className="flex flex-wrap gap-3">
						{territories.map((territory) => (
							<div
								key={territory.id}
								className="inline-flex items-center h-10 gap-2 bg-white hover:bg-gray-100 border border-gray-200 rounded-full px-4 py-3"
							>
								<span className="text-[#6A6A6A] font-medium whitespace-nowrap">
									{territory.name}, {territory.postcode}
								</span>

								<button
									onClick={() => handleRemove(territory.id)}
									disabled={removingId === territory.id}
									className="
										text-red-700 hover:text-red-600
										transition-colors duration-200
										disabled:opacity-50 disabled:cursor-not-allowed
										flex-shrink-0 px-1
									"
									aria-label={`Remove ${territory.name}`}
								>
									<X className="w-5 h-5" />
								</button>
							</div>
						))}
					</div>
				)}
			</div>

			{territories.length > 0 && (
				<div className="flex justify-end">
					{isLoading ? (
						<div className="w-full sm:w-auto h-12 sm:h-[60px] px-4 sm:pl-7 sm:pr-2 rounded-full bg-white text-[#222] inline-flex items-center gap-2 sm:gap-4 shadow-[0_0_150px_#0000003f]">
							<div className="w-4 h-4 sm:w-5 sm:h-5 border-2 border-[#BF2724]/30 border-t-[#BF2724] rounded-full animate-spin flex-shrink-0" />
							<span className="flex-1 text-center sm:text-left font-light text-sm sm:text-[16px]">
								Processing...
							</span>
						</div>
					) : (
						<button
							onClick={handleContinue}
							disabled={isLoading}
							type="button"
							className="w-full sm:w-auto h-12 sm:h-[60px] sm:max-w-[420px] px-4 sm:pl-7 sm:pr-2 rounded-full bg-white text-[#222] hover:bg-[#fafafa] inline-flex items-center gap-2 sm:gap-4 shadow-[0_0_150px_#0000003f]"
						>
							<span className="flex-1 text-center sm:text-left font-light text-sm sm:text-[16px]">
								Continue with{" "}
								<span className="font-semibold text-[#222]">
									{projectCount}
								</span>{" "}
								potential projects
							</span>
							<span
								className="w-8 h-8 sm:w-[42px] sm:h-[42px] grid place-items-center rounded-full bg-[#BF2724] hover:bg-[#aa0000] transition-colors flex-shrink-0"
								aria-label="Continue"
							>
								<ChevronRight className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
							</span>
						</button>
					)}
				</div>
			)}
		</div>
	)
}
