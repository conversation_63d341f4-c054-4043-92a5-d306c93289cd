import { X, MapP<PERSON>, Building2, ChevronRight } from "lucide-react"
import { useState } from "react"

type Territory = {
	id: string
	name: string
	postcode: string
}

interface SelectedTerritoriesProps {
	territories: Territory[]
	onRemove: (id: string) => void
	projectCount?: number
}

export default function SelectedTerritories({
	territories,
	onRemove,
	projectCount = 0,
}: SelectedTerritoriesProps) {
	const [removingId, setRemovingId] = useState<string | null>(null)
	const [isLoading, setIsLoading] = useState(false)

	const handleRemove = async (id: string) => {
		setRemovingId(id)
		// Add a small delay for smooth animation
		setTimeout(() => {
			onRemove(id)
			setRemovingId(null)
		}, 200)
	}

	const handleContinue = () => {
		setIsLoading(true)
		// Simulate loading state
		setTimeout(() => {
			setIsLoading(false)
			console.log("Continue with projects")
		}, 1000)
	}

	return (
		<div className="h-full bg-neutral-50 rounded-[20px] shadow-[0_0_150px_#0000003f] p-8 flex flex-col backdrop-blur-sm">
			<h2 className="text-2xl mb-8 font-semibold text-gray-900">
				Selected Territories
			</h2>

			<div className="flex-1 overflow-y-auto mb-8">
				{territories.length === 0 ? (
					<div className="flex flex-col items-center justify-center h-full text-center py-12">
						<div className="p-6 bg-gray-200/50 rounded-full mb-4">
							<MapPin className="w-12 h-12 text-gray-400" />
						</div>
						<p className="text-gray-500 text-lg font-medium mb-2">
							No territories selected
						</p>
						<p className="text-gray-400 text-sm max-w-48">
							Click on territories on the map to add them to your selection
						</p>
					</div>
				) : (
					<div className="flex flex-wrap gap-3">
						{territories.map((territory) => (
							<div
								key={territory.id}
								className="inline-flex items-center h-10 gap-2 bg-white hover:bg-gray-100 border border-gray-200 rounded-full px-4 py-3"
							>
								<span className="text-[#6A6A6A] font-medium whitespace-nowrap">
									{territory.name}, {territory.postcode}
								</span>

								<button
									onClick={() => handleRemove(territory.id)}
									disabled={removingId === territory.id}
									className="
										text-red-700 hover:text-red-600
										transition-colors duration-200
										disabled:opacity-50 disabled:cursor-not-allowed
										flex-shrink-0 px-1
									"
									aria-label={`Remove ${territory.name}`}
								>
									<X className="w-5 h-5" />
								</button>
							</div>
						))}
					</div>
				)}
			</div>

			{/* Action Button */}
			{territories.length > 0 && (
				<button
					onClick={handleContinue}
					disabled={isLoading}
					type="button"
					className="ml-auto w-[42px] h-[42px] grid place-items-center rounded-full bg-[#BF2724] hover:bg-[#aa0000] transition-colors"
					aria-label="Continue"
				>
					<div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />

					<div className="relative flex items-center justify-center gap-3">
						{isLoading ? (
							<>
								<div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
								<span>Processing...</span>
							</>
						) : (
							<div className="h-[60px] w-[420px] pl-7 pr-2 rounded-full bg-white text-[#222] hover:bg-[#fafafa] inline-flex items-center gap-4 shadow-[0_0_150px_#0000003f]">
								<span className="font-light text-[16px]">
									Continue with{" "}
									<span className="font-semibold text-[#222]">
										{projectCount}
									</span>{" "}
									potential projects
								</span>
								<span>
									<ChevronRight className="w-5 h-5 text-white" />
								</span>
							</div>
						)}
					</div>
				</button>
			)}
		</div>
	)
}
