import { useMemo, useState } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'

type ApplyScope = 'all' | 'single'
type props = { onBack?: () => void }

function RedRange({
  id,
  value,
  onChange,
  min,
  max,
  step = 1,
}: {
  id: string
  value: number
  onChange: (v: number) => void
  min: number
  max: number
  step?: number
}) {
  const pct = useMemo(() => ((value - min) * 100) / (max - min), [value, min, max])

  return (
    <input
      id={id}
      type="range"
      min={min}
      max={max}
      step={step}
      value={value}
      onChange={(e) => onChange(Number(e.target.value))}
      className="assump-range w-full h-2.5 rounded-full appearance-none outline-none border-[1px] border-[#eee]"
      style={{
        background: `linear-gradient(to right, #BF2724 0%, #BF2724 ${pct}%, #fff ${pct}%, #fff 100%)`,
      }}
      aria-labelledby={`${id}-label`}
    />
  )
}

export default function AssumptionsPanel({ onBack }: props) {
  const [apply, setApply] = useState<ApplyScope>('all')

  const [startingUptake, setStartingUptake] = useState(60) 
  const [changePerYear, setChangePerYear] = useState(1) 

  const [avgYears, setAvgYears] = useState(20) 
  const [buildUp, setBuildUp] = useState(2) 

  const potentialCount = 457

  return (
    <section className="relative w-full
      h-[clamp(620px,calc(100vh-476px),calc(100vh-476px))]
      min-[1150px]:h-[clamp(620px,calc(100vh-401px),calc(100vh-401px))]
      bg-white rounded-[20px] shadow-[0_0_150px_#0000003f] overflow-hidden">
      <div className="bg-[#fff] rounded-[20px] shadow-[0_0_150px_#0000003f] px-8 py-8">
        <h2 className="text-[20px] md:text-[22px] font-medium text-[#222] mb-6">Fine-Tune Assumptions</h2>

        <div className="mb-6">
          <div className="font-medium text-[16px] text-[#222] mb-3">Apply to</div>
          <div className="flex gap-3">
            <button
              type="button"
              onClick={() => setApply('all')}
              className={`h-9 px-4 rounded-full transition-colors font-light ${
                apply === 'all'
                  ? 'bg-[#f2f2f2] text-[#222]'
                  : 'bg-white text-[#6a6a6a] border border-[#ebebeb] hover:text-[#222]'
              }`}
            >
              All selected postcodes
            </button>
            <button
              type="button"
              onClick={() => setApply('single')}
              className={`h-9 px-4 rounded-full transition-colors font-light ${
                apply === 'single'
                  ? 'bg-[#f2f2f2] text-[#222]'
                  : 'bg-white text-[#6a6a6a] border border-[#ebebeb] hover:text-[#222]'
              }`}
            >
              Single postcode
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-40">
          <div className="w-full md:w-[530px] justify-self-start">
            <h3 className="font-medium text-[#222] mb-1">Uptake over time</h3>

            <div className="mb-2">
              <div className="flex items-center justify-between mb-1">
                <label id="starting-uptake-label" htmlFor="starting-uptake" className="font-light text-[16px] text-[#6a6a6a]">
                  Starting uptake
                </label>
                <span className="font-light text-[16px] text-[#6a6a6a]">{startingUptake}%</span>
              </div>
              <RedRange
                id="starting-uptake"
                value={startingUptake}
                onChange={setStartingUptake}
                min={0}
                max={100}
                step={1}
              />
            </div>

            <div className="mb-2">
              <div className="flex items-center justify-between mb-1">
                <label id="change-per-year-label" htmlFor="change-per-year" className="font-light text-[16px] text-[#6a6a6a]">
                  Change per year
                </label>
                <span className="text-sm text-[#6a6a6a]">
                  {changePerYear >= 0 ? '+' : ''}
                  {changePerYear}%
                </span>
              </div>
              <RedRange
                id="change-per-year"
                value={changePerYear}
                onChange={setChangePerYear}
                min={-5}
                max={5}
                step={0.5}
              />
            </div>

            <p className="mt-7 font-light text-[16px] text-[#6a6a6a]">
              Uptake means how common a project is in an area.
            </p>
          </div>

          <div className="w-full md:w-[530px] justify-self-start">
            <h3 className="font-medium text-[#222] mb-1">Product Life</h3>

            <div className="mb-2">
              <div className="flex items-center justify-between mb-1">
                <label id="avg-years-label" htmlFor="avg-years" className="font-light text-[16px] text-[#6a6a6a]">
                  Average years
                </label>
                <span className="font-light text-[16px] text-[#6a6a6a]">{avgYears} years</span>
              </div>
              <RedRange
                id="avg-years"
                value={avgYears}
                onChange={setAvgYears}
                min={5}
                max={30}
                step={1}
              />
            </div>

            <div className="mb-2">
              <div className="flex items-center justify-between mb-1">
                <label id="build-up-label" htmlFor="build-up" className="font-light text-[16px] text-[#6a6a6a]">
                  Build-up period
                </label>
                <span className="font-light text-[16px] text-[#6a6a6a]">{buildUp} years</span>
              </div>
              <RedRange
                id="build-up"
                value={buildUp}
                onChange={setBuildUp}
                min={0}
                max={10}
                step={1}
              />
            </div>

            <p className="mt-7 font-light text-[16px] text-[#6a6a6a]">
              Product life means how long before it usually needs doing again.
            </p>
          </div>
        </div>

        <div className="mt-41 flex items-center justify-between">
          <button
            type="button"
            onClick={() => onBack?.()}
            className="h-[60px] w-[310px] pl-2 pr-8 rounded-full bg-white text-[#222] hover:bg-[#fafafa] inline-flex items-center gap-2 shadow-[0_0_150px_#0000003f]"
          >
            <span className="grid place-items-center w-10 h-10 rounded-full bg-[#f2f2f2]">
              <ChevronLeft className="w-5 h-5 text-[#000]" />
            </span>
            <span className="flex-1 text-right font-light text-[16px]">
              Return to <span className="font-semibold">Territory Builder</span>
            </span>
          </button>

          <div className="h-[60px] w-[420px] pl-7 pr-2 rounded-full bg-white text-[#222] hover:bg-[#fafafa] inline-flex items-center gap-4 shadow-[0_0_150px_#0000003f]">
            <span className="font-light text-[16px]">
              Continue with <span className="font-semibold text-[#222]">{potentialCount}</span> potential projects
            </span>
            <button
              type="button"
              className="ml-auto w-[42px] h-[42px] grid place-items-center rounded-full bg-[#BF2724] hover:bg-[#aa0000] transition-colors"
              aria-label="Continue"
            >
              <ChevronRight className="w-5 h-5 text-white" />
            </button>
          </div>
        </div>
      </div>

      <style>{`
        .assump-range::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 40px; height: 24px; border-radius: 12px;
          background: #fff; border: 1px solid #eee;
          cursor: pointer;
        }
        .assump-range::-moz-range-thumb {
          width: 40px; height: 18px; border-radius: 12px;
          background: #fff; border: 1px solid #eee;
          cursor: pointer;
        }
      `}</style>
    </section>
  )
}